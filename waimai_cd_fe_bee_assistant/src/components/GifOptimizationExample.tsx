import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Platform } from 'react-native';
import RNImage from './RNImage';
import OptimizedGifImage, { clearGifPreloadCache, getGifPreloadCacheStatus } from './OptimizedGifImage';
import defaultIcon from '@/assets/images/homeRefactor/defaultWithoutText.gif';
import WebView from '@mrn/mrn-webview';

/**
 * GIF优化示例和性能对比
 */
const GifOptimizationExample: React.FC = () => {
    const [loadTimes, setLoadTimes] = useState<{
        standard: number | null;
        optimized: number | null;
    }>({
        standard: null,
        optimized: null,
    });
    
    const [cacheStatus, setCacheStatus] = useState<any>(null);
    const [showComparison, setShowComparison] = useState(false);

    // 更新缓存状态
    const updateCacheStatus = () => {
        setCacheStatus(getGifPreloadCacheStatus());
    };

    useEffect(() => {
        updateCacheStatus();
        const interval = setInterval(updateCacheStatus, 2000);
        return () => clearInterval(interval);
    }, []);

    // 测试加载时间
    const testLoadTime = (type: 'standard' | 'optimized') => {
        const startTime = Date.now();
        
        const handleLoad = () => {
            const endTime = Date.now();
            const loadTime = endTime - startTime;
            
            setLoadTimes(prev => ({
                ...prev,
                [type]: loadTime,
            }));
        };

        return handleLoad;
    };

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>iOS GIF 加载优化</Text>
            
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>🎯 优化策略</Text>
                <View style={styles.strategyList}>
                    <Text style={styles.strategyItem}>• <Text style={styles.bold}>预加载</Text>：使用 Image.prefetch 预加载GIF</Text>
                    <Text style={styles.strategyItem}>• <Text style={styles.bold}>缓存优化</Text>：强制缓存和HTTP缓存头</Text>
                    <Text style={styles.strategyItem}>• <Text style={styles.bold}>内存优化</Text>：渐进式渲染和硬件加速</Text>
                    <Text style={styles.strategyItem}>• <Text style={styles.bold}>加载状态</Text>：显示加载进度和错误处理</Text>
                    <Text style={styles.strategyItem}>• <Text style={styles.bold}>平台特化</Text>：iOS专用优化参数</Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>📊 缓存状态</Text>
                <View style={styles.cacheInfo}>
                    <Text style={styles.cacheText}>
                        缓存项数量: {cacheStatus?.size || 0}
                    </Text>
                    <TouchableOpacity 
                        style={styles.clearButton}
                        onPress={() => {
                            clearGifPreloadCache();
                            updateCacheStatus();
                        }}
                    >
                        <Text style={styles.clearButtonText}>清理缓存</Text>
                    </TouchableOpacity>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>🔄 性能对比</Text>
                <TouchableOpacity 
                    style={styles.testButton}
                    onPress={() => setShowComparison(!showComparison)}
                >
                    <Text style={styles.testButtonText}>
                        {showComparison ? '隐藏对比' : '显示性能对比'}
                    </Text>
                </TouchableOpacity>
                
                {showComparison && (
                    <>
                        <View style={styles.comparisonContainer}>
                            <View style={styles.comparisonItem}>
                                <Text style={styles.comparisonTitle}>标准 RNImage</Text>
                                <RNImage
                                    source={defaultIcon}
                                    style={styles.gifImage}
                                    onLoad={testLoadTime('standard')}
                                />
                                <Text style={styles.loadTime}>
                                    加载时间: {loadTimes.standard ? `${loadTimes.standard}ms` : '测试中...'}
                                </Text>
                            </View>
                            
                            <View style={styles.comparisonItem}>
                                <Text style={styles.comparisonTitle}>优化版本</Text>
                                <OptimizedGifImage
                                    source={defaultIcon}
                                    style={styles.gifImage}
                                    enablePreload={true}
                                    showLoadingIndicator={true}
                                    onLoad={testLoadTime('optimized')}
                                />
                                <Text style={styles.loadTime}>
                                    加载时间: {loadTimes.optimized ? `${loadTimes.optimized}ms` : '测试中...'}
                                </Text>
                            </View>
                        </View>
                        <View style={styles.comparisonContainer}>
                            <View style={styles.comparisonItem}>
                                <Text style={styles.comparisonTitle}>标准 RNImage</Text>
                                <WebView
                                    source={defaultIcon}
                                    style={styles.gifImage}
                                    onLoad={testLoadTime('standard')}
                                />
                                <Text style={styles.loadTime}>
                                    加载时间: {loadTimes.standard ? `${loadTimes.standard}ms` : '测试中...'}
                                </Text>
                            </View>
                            
                            <View style={styles.comparisonItem}>
                                <Text style={styles.comparisonTitle}>优化版本</Text>
                                <OptimizedGifImage
                                    source={defaultIcon}
                                    style={styles.gifImage}
                                    enablePreload={true}
                                    showLoadingIndicator={true}
                                    onLoad={testLoadTime('optimized')}
                                />
                                <Text style={styles.loadTime}>
                                    加载时间: {loadTimes.optimized ? `${loadTimes.optimized}ms` : '测试中...'}
                                </Text>
                            </View>
                        </View>
                    </>
                )}
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>💡 使用建议</Text>
                <View style={styles.tipsList}>
                    <Text style={styles.tip}>
                        <Text style={styles.bold}>1. 文件大小</Text>：尽量控制GIF文件大小在500KB以内
                    </Text>
                    <Text style={styles.tip}>
                        <Text style={styles.bold}>2. 帧数优化</Text>：减少不必要的帧数，保持流畅度
                    </Text>
                    <Text style={styles.tip}>
                        <Text style={styles.bold}>3. 尺寸适配</Text>：使用合适的显示尺寸，避免过度缩放
                    </Text>
                    <Text style={styles.tip}>
                        <Text style={styles.bold}>4. 预加载时机</Text>：在用户可能看到GIF之前进行预加载
                    </Text>
                    <Text style={styles.tip}>
                        <Text style={styles.bold}>5. 内存管理</Text>：及时清理不需要的GIF缓存
                    </Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>🛠 技术实现</Text>
                <View style={styles.codeBlock}>
                    <Text style={styles.code}>
{`// 基础用法
<OptimizedGifImage
  source={require('./animation.gif')}
  style={{ width: 200, height: 200 }}
  enablePreload={true}
  showLoadingIndicator={true}
/>

// 网络GIF
<OptimizedGifImage
  source={{ uri: 'https://example.com/animation.gif' }}
  style={{ width: 150, height: 150 }}
  fallbackSource={require('./fallback.png')}
/>`}
                    </Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>📱 平台差异</Text>
                <Text style={styles.platformInfo}>
                    当前平台: <Text style={styles.bold}>{Platform.OS}</Text>
                </Text>
                <Text style={styles.description}>
                    {Platform.OS === 'ios' 
                        ? '✅ iOS平台已启用GIF优化功能'
                        : '⚠️ 当前平台不是iOS，部分优化功能可能不生效'
                    }
                </Text>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        padding: 16,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: '#333',
    },
    section: {
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 12,
        color: '#333',
    },
    strategyList: {
        paddingLeft: 8,
    },
    strategyItem: {
        fontSize: 14,
        color: '#555',
        marginBottom: 6,
        lineHeight: 20,
    },
    bold: {
        fontWeight: 'bold',
        color: '#333',
    },
    cacheInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    cacheText: {
        fontSize: 14,
        color: '#666',
    },
    clearButton: {
        backgroundColor: '#ff6b6b',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
    },
    clearButtonText: {
        color: 'white',
        fontSize: 12,
        fontWeight: 'bold',
    },
    testButton: {
        backgroundColor: '#4ecdc4',
        paddingVertical: 12,
        borderRadius: 6,
        alignItems: 'center',
        marginBottom: 16,
    },
    testButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },
    comparisonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
    },
    comparisonItem: {
        alignItems: 'center',
        flex: 1,
        marginHorizontal: 8,
    },
    comparisonTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        marginBottom: 8,
        color: '#333',
    },
    gifImage: {
        width: 100,
        height: 100,
        borderRadius: 8,
        marginBottom: 8,
    },
    loadTime: {
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
    },
    tipsList: {
        paddingLeft: 8,
    },
    tip: {
        fontSize: 14,
        color: '#555',
        marginBottom: 8,
        lineHeight: 20,
    },
    codeBlock: {
        backgroundColor: '#f8f9fa',
        borderRadius: 6,
        padding: 12,
        borderLeftWidth: 4,
        borderLeftColor: '#007acc',
    },
    code: {
        fontSize: 12,
        color: '#333',
        fontFamily: 'monospace',
        lineHeight: 18,
    },
    platformInfo: {
        fontSize: 14,
        color: '#666',
        marginBottom: 8,
    },
    description: {
        fontSize: 14,
        color: '#555',
        lineHeight: 20,
    },
});

export default GifOptimizationExample;
