import React, { useState, useEffect, useRef } from 'react';
import { Image, ImageProps, Platform, View, Text, ActivityIndicator } from 'react-native';
import { AsyncStorage } from '@mrn/react-native';
import { debugLog } from '@/utils/debugLog';

interface OptimizedGifImageProps extends ImageProps {
    source: ImageProps['source'];
    enablePreload?: boolean;
    showLoadingIndicator?: boolean;
    fallbackSource?: ImageProps['source'];
}

// GIF预加载缓存
const GIF_PRELOAD_CACHE = new Map<string, boolean>();

/**
 * iOS GIF优化组件
 * 专门针对iOS平台的GIF加载性能优化
 */
const OptimizedGifImage: React.FC<OptimizedGifImageProps> = ({
    source,
    enablePreload = true,
    showLoadingIndicator = true,
    fallbackSource,
    style,
    onLoad,
    onError,
    onLoadStart,
    ...restProps
}) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const [isPreloaded, setIsPreloaded] = useState(false);
    const mountedRef = useRef(true);

    // 获取图片URI
    const getImageUri = (imageSource: any): string | null => {
        if (typeof imageSource === 'string') {
            return imageSource;
        }
        if (imageSource && typeof imageSource === 'object' && imageSource.uri) {
            return imageSource.uri;
        }
        return null;
    };

    // 检查是否为GIF
    const isGif = (imageSource: any): boolean => {
        const uri = getImageUri(imageSource);
        return uri ? uri.toLowerCase().includes('.gif') : false;
    };

    // GIF预加载
    const preloadGif = async (imageSource: any) => {
        if (!enablePreload || Platform.OS !== 'ios' || !isGif(imageSource)) {
            return;
        }

        const uri = getImageUri(imageSource);
        if (!uri) return;

        // 检查是否已经预加载过
        if (GIF_PRELOAD_CACHE.has(uri)) {
            setIsPreloaded(true);
            return;
        }

        try {
            debugLog('开始预加载GIF:', uri);
            
            // 使用Image.prefetch进行预加载
            const success = await Image.prefetch(uri);
            
            if (success && mountedRef.current) {
                GIF_PRELOAD_CACHE.set(uri, true);
                setIsPreloaded(true);
                debugLog('GIF预加载成功:', uri);
            }
        } catch (error) {
            debugLog('GIF预加载失败:', error);
        }
    };

    // 组件挂载时预加载
    useEffect(() => {
        preloadGif(source);
        
        return () => {
            mountedRef.current = false;
        };
    }, [source, enablePreload]);

    // 处理加载开始
    const handleLoadStart = () => {
        setIsLoading(true);
        setHasError(false);
        onLoadStart?.();
    };

    // 处理加载完成
    const handleLoad = (event: any) => {
        if (mountedRef.current) {
            setIsLoading(false);
            setHasError(false);
        }
        onLoad?.(event);
    };

    // 处理加载错误
    const handleError = (error: any) => {
        if (mountedRef.current) {
            setIsLoading(false);
            setHasError(true);
        }
        debugLog('GIF加载错误:', error);
        onError?.(error);
    };

    // 获取优化后的source
    const getOptimizedSource = () => {
        if (hasError && fallbackSource) {
            return fallbackSource;
        }

        if (Platform.OS === 'ios' && isGif(source)) {
            const uri = getImageUri(source);
            if (uri) {
                return {
                    uri,
                    cache: 'force-cache',
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'max-age=86400', // 缓存1天
                        'Accept': 'image/gif,image/*,*/*;q=0.8',
                    },
                };
            }
        }

        return source;
    };

    // iOS GIF特定的props
    const iosGifProps = Platform.OS === 'ios' && isGif(source) ? {
        resizeMode: restProps.resizeMode || 'contain',
        // 启用硬件加速
        renderToHardwareTextureAndroid: false,
        // 优化内存使用
        progressiveRenderingEnabled: true,
        // 减少内存压力
        fadeDuration: 0,
    } : {};

    return (
        <View style={style}>
            <Image
                {...restProps}
                {...iosGifProps}
                source={getOptimizedSource()}
                style={[style, hasError && { opacity: 0.5 }]}
                onLoadStart={handleLoadStart}
                onLoad={handleLoad}
                onError={handleError}
            />
            
            {/* 加载指示器 */}
            {showLoadingIndicator && isLoading && !hasError && (
                <View style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                }}>
                    <ActivityIndicator size="small" color="#666" />
                    <Text style={{
                        marginTop: 8,
                        fontSize: 12,
                        color: '#666',
                        textAlign: 'center',
                    }}>
                        {isGif(source) ? '加载GIF中...' : '加载中...'}
                    </Text>
                </View>
            )}
            
            {/* 错误状态 */}
            {hasError && !fallbackSource && (
                <View style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: 'rgba(240, 240, 240, 0.8)',
                }}>
                    <Text style={{
                        fontSize: 12,
                        color: '#999',
                        textAlign: 'center',
                    }}>
                        加载失败
                    </Text>
                </View>
            )}
            
            {/* 预加载状态指示器（仅开发环境） */}
            {__DEV__ && isGif(source) && (
                <View style={{
                    position: 'absolute',
                    top: 4,
                    right: 4,
                    backgroundColor: isPreloaded ? 'green' : 'orange',
                    borderRadius: 4,
                    paddingHorizontal: 4,
                    paddingVertical: 2,
                }}>
                    <Text style={{
                        fontSize: 8,
                        color: 'white',
                        fontWeight: 'bold',
                    }}>
                        {isPreloaded ? 'CACHED' : 'LOADING'}
                    </Text>
                </View>
            )}
        </View>
    );
};

// 清理GIF预加载缓存
export const clearGifPreloadCache = () => {
    GIF_PRELOAD_CACHE.clear();
    debugLog('GIF预加载缓存已清理');
};

// 获取GIF预加载缓存状态
export const getGifPreloadCacheStatus = () => {
    return {
        size: GIF_PRELOAD_CACHE.size,
        keys: Array.from(GIF_PRELOAD_CACHE.keys()),
    };
};

export default OptimizedGifImage;
