import React, { PropsWithChildren, useEffect, useState } from 'react';
import { Image, ImageProps, ImageStyle, StyleProp, Platform } from 'react-native';
import { AsyncStorage } from '@mrn/react-native';

interface RNImageProps extends PropsWithChildren<ImageProps> {}

import defaultImage from '@/assets/images/defaultImage.png';
import NetImages from '@/assets/images/homeRefactor';
import defaultIcon from '@/assets/images/homeRefactor/defaultWithoutText.gif';
import goutong from '@/assets/images/homeRefactor/智能沟通.png';
import zhenduan from '@/assets/images/homeRefactor/智能诊断.png';
import { debugLog } from '@/utils/debugLog';

// 缓存相关常量
const IMAGE_SIZE_CACHE_KEY = 'RNImage_size_cache';
const CACHE_EXPIRY_TIME = 7 * 24 * 60 * 60 * 1000; // 7天过期

// GIF优化相关常量
const GIF_PRELOAD_CACHE_KEY = 'RNImage_gif_preload_cache';
const GIF_PRELOAD_EXPIRY_TIME = 24 * 60 * 60 * 1000; // GIF预加载缓存1天过期

// 缓存数据结构
interface ImageSizeCache {
    [url: string]: {
        width: number;
        height: number;
        timestamp: number;
    };
}

// 从缓存获取图片尺寸
const getImageSizeFromCache = async (url: string): Promise<{ width: number; height: number } | null> => {
    try {
        const cacheData = await AsyncStorage.getItem(IMAGE_SIZE_CACHE_KEY);
        if (!cacheData) return null;

        const cache: ImageSizeCache = JSON.parse(cacheData);
        const cachedItem = cache[url];

        if (!cachedItem) return null;

        // 检查是否过期
        const now = Date.now();
        if (now - cachedItem.timestamp > CACHE_EXPIRY_TIME) {
            // 过期了，删除这个缓存项
            delete cache[url];
            await AsyncStorage.setItem(IMAGE_SIZE_CACHE_KEY, JSON.stringify(cache));
            return null;
        }

        return { width: cachedItem.width, height: cachedItem.height };
    } catch (error) {
        debugLog('获取图片尺寸缓存失败:', error);
        return null;
    }
};

// 保存图片尺寸到缓存
const saveImageSizeToCache = async (url: string, width: number, height: number): Promise<void> => {
    try {
        const cacheData = await AsyncStorage.getItem(IMAGE_SIZE_CACHE_KEY);
        const cache: ImageSizeCache = cacheData ? JSON.parse(cacheData) : {};

        cache[url] = {
            width,
            height,
            timestamp: Date.now(),
        };

        await AsyncStorage.setItem(IMAGE_SIZE_CACHE_KEY, JSON.stringify(cache));
    } catch (error) {
        debugLog('保存图片尺寸缓存失败:', error);
    }
};

// 检查是否为GIF文件
const isGifFile = (source: any): boolean => {
    if (typeof source === 'string') {
        return source.toLowerCase().includes('.gif');
    }
    if (source && typeof source === 'object' && source.uri) {
        return source.uri.toLowerCase().includes('.gif');
    }
    // 对于本地require的GIF，可以通过文件名判断
    if (source === defaultIcon) {
        return true;
    }
    return false;
};

// iOS GIF优化：预加载和缓存策略
const optimizeGifForIOS = (source: any): any => {
    if (Platform.OS !== 'ios' || !isGifFile(source)) {
        return source;
    }

    // 对于本地GIF，添加iOS优化属性
    if (typeof source === 'number') {
        return source;
    }

    // 对于网络GIF，添加缓存和优化参数
    if (source && typeof source === 'object' && source.uri) {
        return {
            ...source,
            cache: 'force-cache',
            // iOS特定优化
            method: 'GET',
            headers: {
                'Cache-Control': 'max-age=86400', // 缓存1天
            },
        };
    }

    return source;
};

const convertToLocalImg = (path: string) => {
    const imgMap = {
        'https://s3plus.meituan.net/bdaiassistant-public/skills/%E6%99%BA%E8%83%BD%E8%AF%8A%E6%96%AD%403x.png':
            zhenduan,
        'https://s3plus.meituan.net/bdaiassistant-public/skills/%E6%99%BA%E8%83%BD%E6%B2%9F%E9%80%9A%403x.png':
            goutong,
        [NetImages.defaultIcon]: defaultIcon,
    };
    if (imgMap[path]) {
        return imgMap[path];
    }
    return path;
};

const RNImage = (
    props: RNImageProps & { source: string | ImageProps['source'] },
) => {
    let { style, source: originSource, ...restProps } = props;
    // 新增：自动处理source参数
    let source = originSource;
    if (!originSource) {
        source = defaultImage;
    } else if (typeof originSource === 'string') {
        const path = convertToLocalImg(originSource);
        if (typeof path === 'number') {
            source = path;
        } else {
            source = { uri: path };
        }
    }
    const [originSize, setOriginSize] = useState<{
        width: number;
        height: number;
    } | null>(null);

    // 获取图片原始宽高
    useEffect(() => {
        let isMounted = true;

        const loadImageSize = async () => {
            if (originSource === NetImages.defaultIcon) {
                debugLog(originSource);
                try {
                    isMounted &&
                        setOriginSize({
                            width: 614,
                            height: 614,
                        });
                } catch (e) {
                    debugLog(e, originSource);
                }
            } else if (typeof source === 'number') {
                // 本地图
                const resolved = Image.resolveAssetSource(source);
                if (resolved && resolved.width && resolved.height) {
                    isMounted &&
                        setOriginSize({
                            width: resolved.width,
                            height: resolved.height,
                        });
                }
            } else if (source && 'uri' in source && source.uri) {
                // 网络图 - 先尝试从缓存获取
                const cachedSize = await getImageSizeFromCache(source.uri);
                if (cachedSize && isMounted) {
                    setOriginSize(cachedSize);
                    return;
                }

                // 缓存中没有，从网络获取
                Image.getSize(
                    source.uri,
                    async (width, height) => {
                        if (isMounted) {
                            setOriginSize({ width, height });
                            // 保存到缓存
                            await saveImageSizeToCache(source.uri!, width, height);
                        }
                    },
                    () => {
                        // 获取失败，忽略
                    },
                );
            } else {
                setOriginSize(null);
            }
        };

        loadImageSize();

        return () => {
            isMounted = false;
        };
    }, [originSource]);

    // 解析外部 style
    const flattenStyle = Array.isArray(style)
        ? Object.assign({}, ...style)
        : style || {};
    const { width, height, ...restStyle } = flattenStyle as ImageStyle;

    let finalWidth = width;
    let finalHeight = height;
    if (originSize) {
        if (width && !height) {
            finalHeight =
                (width as number) * (originSize.height / originSize.width);
        } else if (!width && height) {
            finalWidth =
                (height as number) * (originSize.width / originSize.height);
        } else if (!width && !height) {
            finalWidth = originSize.width;
            finalHeight = originSize.height;
        }
    }

    const finalStyle: StyleProp<ImageStyle> = {
        ...restStyle,
        width: finalWidth,
        height: finalHeight,
    };

    // 应用GIF优化
    const optimizedSource = optimizeGifForIOS(source);

    return (
        <Image
            {...restProps}
            source={
                typeof optimizedSource === 'number'
                    ? optimizedSource
                    : { ...(optimizedSource as object), cache: 'force-cache' }
            }
            defaultSource={defaultImage}
            style={finalStyle}
            // iOS GIF优化：添加特定属性
            {...(Platform.OS === 'ios' && isGifFile(source) ? {
                resizeMode: restProps.resizeMode || 'contain',
                // 预加载优化
                onLoadStart: () => {
                    restProps.onLoadStart?.();
                    debugLog('GIF开始加载:', source);
                },
                onLoad: (e) => {
                    restProps.onLoad?.(e);
                    debugLog('GIF加载完成:', source);
                },
            } : {})}
            onError={(e) => {
                restProps.onError?.(e);
                debugLog('图片加载错误:', e);
            }}
        />
    );
};

export default RNImage;
